import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput } from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Building2, Bed, Bath, ListChecks, CircleAlert as AlertCircle, Globe, DollarSign } from 'lucide-react-native';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { api, fetchPriceRanges, fetchLead } from '@/lib/api';
import { useListingTypes, useAmenities, useBedrooms, useBathrooms } from '@/hooks/useConfiguration';
import Button from '@/components/Button';
import { KeyValue, ListingType, LeadWrite } from '@/types/global';
import { useProfile } from '@/contexts/ProfileContext';
import LeadSourceSelector from '@/components/LeadSourceSelector';
import PriceSelector from '@/components/PriceSelector';
import AdTypeSelector from '@/components/AdTypeSelector';

interface LeadSource {
  id: number;
  name: string;
  description: string | null;
}

interface LeadDetails {
  propertyTypes: number[];
  bedrooms: Array<string>;
  bathrooms: string;
  selectedAmenities: string[];
  requirements: string;
  leadSource: LeadSource | null;
  minPrice: number | null;
  maxPrice: number | null;
}

interface ValidationErrors {
  [key: string]: string[];
}

export default function LeadDetails() {
  const params = useLocalSearchParams();
  const { data: propertyTypes = [], isLoading: isLoadingTypes } = useListingTypes();
  const { data: amenities = [], isLoading: isLoadingAmenities } = useAmenities();
  const { data: bedrooms = [], isLoading: isLoadingBedrooms } = useBedrooms();
  const { data: bathrooms = [], isLoading: isLoadingBathrooms } = useBathrooms();
  const { profile } = useProfile();
  const queryClient = useQueryClient();
  const [isSourceSelectorVisible, setIsSourceSelectorVisible] = useState(false);

  // Check if we're in edit mode or details mode
  const isEditMode = params.mode === 'edit' && params.leadId;
  const isDetailsMode = params.mode === 'details' && params.leadId;
  const leadId = params.leadId ? parseInt(params.leadId as string, 10) : null;

  // For edit mode, get adType from params, for create mode get from params
  const [selectedAdType, setSelectedAdType] = useState<'rent' | 'sale'>(params.adType as 'rent' | 'sale' || 'rent');
  const adType = selectedAdType;
  const { minPrices, maxPrices } = fetchPriceRanges(adType);

  // Fetch lead data if in edit mode or details mode
  const { data: leadData } = useQuery({
    queryKey: ['lead', leadId],
    queryFn: () => fetchLead(leadId!),
    enabled: (isEditMode || isDetailsMode) && !!leadId,
  });

  if (!profile?.id) {
    throw new Error('User profile not found');
  }


  const [details, setDetails] = useState<LeadDetails>({
    propertyTypes: [],
    bedrooms: [],
    bathrooms: '',
    selectedAmenities: [],
    requirements: '',
    leadSource: null,
    minPrice: null,
    maxPrice: null,
  });

  const [validationErrors, setValidationErrors] = useState<ValidationErrors | null>(null);
  const [apiError, setApiError] = useState<string | null>(null);

  // Preload lead data when in details mode
  useEffect(() => {
    if (isDetailsMode && leadData) {
      try {
        // Parse leads_request if it exists
        let leadsRequest = null;
        if ((leadData as any).leads_request) {
          leadsRequest = JSON.parse((leadData as any).leads_request);
        }

        // Set the form with current lead values
        setDetails(prev => ({
          ...prev,
          propertyTypes: leadData.filter_property_type ? [leadData.filter_property_type] : prev.propertyTypes,
          bedrooms: leadsRequest?.be ? [leadsRequest.be] : prev.bedrooms,
          bathrooms: leadsRequest?.ba || prev.bathrooms,
          selectedAmenities: leadsRequest?.a || prev.selectedAmenities,
          requirements: leadData.requirements || prev.requirements,
          minPrice: leadData.filter_budget_min || prev.minPrice,
          maxPrice: leadData.filter_budget_max || prev.maxPrice,
        }));

        // Set the operation type
        if (leadData.filter_operation_type) {
          setSelectedAdType(leadData.filter_operation_type as 'rent' | 'sale');
        }
      } catch (error) {
        console.error('Error parsing lead data:', error);
      }
    }
  }, [isDetailsMode, leadData]);

  // Function to handle details mode - return to details screen with selected data
  const handleDetailsModeSave = () => {
    // Include the operation type in the details
    const completeDetails = {
      ...details,
      operationType: selectedAdType
    };

    router.back();
    router.setParams({
      updatedRequestDetails: JSON.stringify(completeDetails)
    });
  };

  const { mutate: saveLeadDetails, isPending } = useMutation({
    mutationFn: async () => {
      try {
        setValidationErrors(null);
        setApiError(null);

        if (isEditMode && leadId) {
          // Update existing lead
          const updateData = {
            ad_type: selectedAdType,
            request: {
              propertyTypes: details.propertyTypes,
              operationType: selectedAdType,
              amenities: details.selectedAmenities.map(a => parseInt(a, 10)),
              bedrooms: details.bedrooms,
              bathrooms: details.bathrooms,
              minPrice: details.minPrice,
              maxPrice: details.maxPrice,
            },
            remarks: details.requirements || 'No specific requirements',
          };

          const response = await api.put(`/leads/${leadId}`, updateData);
          return response.data;
        } else {
          // Create new lead
          const contact = JSON.parse(params.contact as string);
          const adType = params.adType as string;

          const leadData: LeadWrite = {
            contact_id: contact.id,
            status_id: 11, // New status
            ad_type: adType,
            agent_id: profile.id,
            platform_from: details.leadSource?.id,
            remarks: details.requirements || 'No specific requirements',
            request: {
              propertyTypes: details.propertyTypes,
              operationType: adType as 'rent' | 'sale',
              amenities: details.selectedAmenities.map(a => parseInt(a, 10)),
              bedrooms: details.bedrooms,
              bathrooms: details.bathrooms,
              balcony: false,
              kitchen: '',
              listingViews: [],
              maxArea: 0,
              maxPrice: details.maxPrice || 0,
              minArea: 0,
              minPrice: details.minPrice || 0,
              pantry: '',
              parking: false,
              requirements: details.requirements
            }
          };

          const response = await api.post('/leads', leadData);
          return response.data;
        }
      } catch (error: any) {
        if (error.response?.data?.errors) {
          setValidationErrors(error.response.data.errors);
        } else {
          setApiError(error.response?.data?.message || error.message || 'Failed to save lead. Please try again.');
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      if (isEditMode) {
        // Invalidate queries and go back to lead details
        queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
        queryClient.invalidateQueries({ queryKey: ['leads'] });
        router.back();
      } else {
        // Go to next step for new lead creation
        router.replace({
          pathname: '/leads/create/next-step',
          params: { leadId: data.id }
        });
      }
    },
  });

  const togglePropertyType = (id: number) => {
    setDetails(prev => ({
      ...prev,
      propertyTypes: prev.propertyTypes.includes(id)
        ? prev.propertyTypes.filter(typeId => typeId !== id)
        : [...prev.propertyTypes, id]
    }));
  };

  const toggleBedrooms = (key: string) => {
    setDetails(prev => ({
      ...prev,
      bedrooms: prev.bedrooms.includes(key)
        ? prev.bedrooms.filter(bedroomKey => bedroomKey !== key)
        : [...prev.bedrooms, key]
    }));
  };

  const toggleAmenity = (amenity: string) => {
    setDetails(prev => ({
      ...prev,
      selectedAmenities: prev.selectedAmenities.includes(amenity)
        ? prev.selectedAmenities.filter(a => a !== amenity)
        : [...prev.selectedAmenities, amenity]
    }));
  };

  const renderErrors = () => {
    if (apiError) {
      return (
        <View style={styles.errorBanner}>
          <AlertCircle size={20} color="#DC2626" />
          <Text style={styles.errorText}>{apiError}</Text>
        </View>
      );
    }

    if (validationErrors) {
      return (
        <View style={styles.errorBanner}>
          {Object.entries(validationErrors).map(([field, errors]) => (
            <View key={field} style={styles.errorItem}>
              <Text style={styles.errorField}>
                {field.split('.').map(part =>
                  part.charAt(0).toUpperCase() + part.slice(1)
                ).join(' ')}:
              </Text>
              {errors.map((error, index) => (
                <Text key={index} style={styles.errorText}>
                  • {error}
                </Text>
              ))}
            </View>
          ))}
        </View>
      );
    }

    return null;
  };

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <Button
                variant="ghost"
                icon={<ArrowLeft size={24} color="#111827" />}
                onPress={() => router.back()}
              />
              <Text style={styles.headerTitle}>
                {isDetailsMode ? "Edit Request Details" : "Lead Details"}
              </Text>
            </View>
          ),
        }}
      />

      <View style={styles.container}>
        <ScrollView style={styles.content}>
          {renderErrors()}

          {(isEditMode || isDetailsMode) ? (
            // Show Ad Type Selector in edit/details mode
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Building2 size={20} color="#6B7280" />
                <Text style={styles.sectionTitle}>Operation Type</Text>
              </View>
              <AdTypeSelector
                selectedType={selectedAdType}
                onSelectType={setSelectedAdType}
              />
            </View>
          ) : (
            // Show Lead Source in create mode
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Globe size={20} color="#6B7280" />
                <Text style={styles.sectionTitle}>Lead Source</Text>
              </View>
            {details.leadSource ? (
              <View style={styles.selectedSource}>
                <View style={styles.sourceInfo}>
                  <Text style={styles.sourceName}>{details.leadSource.name}</Text>
                  {details.leadSource.description && (
                    <Text style={styles.sourceDescription}>
                      {details.leadSource.description}
                    </Text>
                  )}
                </View>
                <Button
                  variant="secondary"
                  label="Change"
                  onPress={() => setIsSourceSelectorVisible(true)}
                />
              </View>
            ) : (
              <Button
                label="Select Source"
                icon={<Globe size={20} color="#fff" />}
                onPress={() => setIsSourceSelectorVisible(true)}
                fullWidth
              />
            )}
            </View>
          )}

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Budget Range</Text>
            <View style={styles.priceSelectors}>
              <PriceSelector
                title="Minimum Price"
                discriminant={`minPrice${adType}`}
                values={minPrices}
                selectedValue={details.minPrice}
                onSelect={(value) => setDetails(prev => ({ ...prev, minPrice: value }))}
                formatPrice={(price) => new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'QAR',
                  maximumFractionDigits: 0,
                }).format(price)}
              />
              
              <PriceSelector
                title="Maximum Price"
                discriminant={`maxPrice${adType}`}
                values={maxPrices}
                selectedValue={details.maxPrice}
                onSelect={(value) => setDetails(prev => ({ ...prev, maxPrice: value }))}
                formatPrice={(price) => new Intl.NumberFormat('en-US', {
                  style: 'currency',
                  currency: 'QAR',
                  maximumFractionDigits: 0,
                }).format(price)}
              />
            </View>
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Building2 size={20} color="#6B7280" />
              <Text style={styles.sectionTitle}>Property Types</Text>
            </View>
            <View style={styles.propertyTypes}>
              {isLoadingTypes ? (
                <Text style={styles.loadingText}>Loading property types...</Text>
              ) : (
                propertyTypes.map((type: ListingType) => (
                  <Button
                    key={type.id}
                    label={type.label}
                    variant={details.propertyTypes.includes(type.id) ? 'primary' : 'secondary'}
                    onPress={() => togglePropertyType(type.id)}
                  />
                ))
              )}
            </View>
          </View>

          <View style={styles.section}>
            <View style={styles.row}>
              <View style={styles.inputGroup}>
                <View style={styles.inputHeader}>
                  <Bed size={20} color="#6B7280" />
                  <Text style={styles.inputLabel}>Bedrooms</Text>
                </View>
                <View style={styles.optionsGrid}>
                  {isLoadingBedrooms ? (
                    <Text style={styles.loadingText}>Loading...</Text>
                  ) : (
                    bedrooms.map((option: KeyValue) => (
                      <Button
                        key={option.key}
                        label={option.value}
                        variant={details.bedrooms.includes(option.key) ? 'primary' : 'secondary'}
                        onPress={() => toggleBedrooms(option.key)}
                      />
                    ))
                  )}
                </View>
              </View>

              <View style={styles.inputGroup}>
                <View style={styles.inputHeader}>
                  <Bath size={20} color="#6B7280" />
                  <Text style={styles.inputLabel}>Bathrooms</Text>
                </View>
                <View style={styles.optionsGrid}>
                  {isLoadingBathrooms ? (
                    <Text style={styles.loadingText}>Loading...</Text>
                  ) : (
                    bathrooms.map((option: KeyValue) => (
                      <Button
                        key={option.key}
                        label={option.value}
                        variant={details.bathrooms === option.value ? 'primary' : 'secondary'}
                        onPress={() => setDetails(prev => ({ ...prev, bathrooms: option.value }))}
                      />
                    ))
                  )}
                </View>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <ListChecks size={20} color="#6B7280" />
              <Text style={styles.sectionTitle}>Amenities</Text>
            </View>
            <View style={styles.amenities}>
              {isLoadingAmenities ? (
                <Text style={styles.loadingText}>Loading amenities...</Text>
              ) : (
                amenities.map((amenity: KeyValue) => (
                  <Button
                    key={amenity.key}
                    label={amenity.value}
                    variant={details.selectedAmenities.includes(amenity.key) ? 'primary' : 'secondary'}
                    onPress={() => toggleAmenity(amenity.key)}
                  />
                ))
              )}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Additional Requirements</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={details.requirements}
              onChangeText={(text) => setDetails(prev => ({ ...prev, requirements: text }))}
              placeholder="Enter any specific requirements or preferences"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <Button
            label={isDetailsMode ? "Update Request Details" : "Save Lead"}
            onPress={isDetailsMode ? handleDetailsModeSave : () => saveLeadDetails()}
            disabled={isDetailsMode ? details.propertyTypes.length === 0 : (isPending || details.propertyTypes.length === 0)}
            loading={isDetailsMode ? false : isPending}
            fullWidth
          />
        </View>
      </View>

      <LeadSourceSelector
        visible={isSourceSelectorVisible}
        onClose={() => setIsSourceSelectorVisible(false)}
        onSelect={(source) => {
          setDetails(prev => ({ ...prev, leadSource: source }));
          setIsSourceSelectorVisible(false);
        }}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  errorBanner: {
    backgroundColor: '#FEF2F2',
    padding: 16,
    margin: 20,
    marginBottom: 0,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FEE2E2',
    gap: 12,
  },
  errorItem: {
    marginBottom: 8,
  },
  errorField: {
    fontSize: 14,
    fontWeight: '600',
    color: '#991B1B',
    marginBottom: 4,
  },
  errorText: {
    fontSize: 14,
    color: '#DC2626',
    marginLeft: 8,
  },
  section: {
    margin: 20,
    marginBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
  },
  propertyTypes: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  row: {
    flexDirection: 'row',
    gap: 16,
  },
  inputGroup: {
    flex: 1,
  },
  inputHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#4B5563',
    marginBottom: 8,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: '#1F2937',
  },
  textArea: {
    height: 100,
    marginTop: 8,
  },
  amenities: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  footer: {
    padding: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#fff',
  },
  loadingText: {
    color: '#6B7280',
    fontSize: 14,
    fontStyle: 'italic',
  },
  selectedSource: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  sourceInfo: {
    flex: 1,
    marginRight: 16,
  },
  sourceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  sourceDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
  priceInputContainer: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    padding: 12,
  },
  priceInput: {
    fontSize: 16,
    color: '#1F2937',
    padding: 0,
  },
  formattedPrice: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  priceSelectors: {
    gap: 24,
  },
});