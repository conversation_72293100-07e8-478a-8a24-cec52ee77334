import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import Button from '@/components/Button';
import ContactSelector from '@/components/ContactSelector';

type Contact = {
  id: number;
  name: string;
  email_1: string | null;
  prefix_mobile_1: string | null;
  mobile_1: string | null;
  company_name: string | null;
};

export default function SelectContact() {
  const { leadId, mode } = useLocalSearchParams();
  const queryClient = useQueryClient();
  const isEditMode = mode === 'edit' && leadId;

  // Get current lead data to preserve existing values
  const { data: currentLead } = useQuery({
    queryKey: ['lead', leadId],
    queryFn: () => api.get(`/leads/${leadId}`).then(res => res.data),
    enabled: isEditMode && !!leadId,
  });

  // Mutation for updating lead contact
  const updateContactMutation = useMutation({
    mutationFn: async (contactId: number) => {
      console.log('Updating lead contact:', {
        leadId,
        contactId,
        url: `/leads/${leadId}`,
        payload: { contact_id: contactId }
      });

      try {
        // Based on your PHP validation, we need to include required fields
        // Use current lead data to preserve existing values
        const payload = {
          contact_id: contactId,
          remarks: currentLead?.requirements || 'Contact updated via mobile app',
          request: {
            propertyTypes: [currentLead?.filter_property_type || 1],
            operationType: currentLead?.filter_operation_type || 'rent'
          }
        };

        console.log('Sending payload:', payload);

        // Try PATCH first, then PUT if that fails
        let response;
        try {
          response = await api.patch(`/leads/${leadId}`, payload);
        } catch (patchError: any) {
          console.log('PATCH failed, trying PUT:', patchError.response?.status);
          response = await api.put(`/leads/${leadId}`, payload);
        }
        console.log('Contact update success:', response.data);
        return response.data;
      } catch (error: any) {
        console.error('Contact update error details:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers
        });

        // If 405, try POST method as fallback
        if (error.response?.status === 405) {
          console.log('Trying POST method as fallback...');
          try {
            const postResponse = await api.post(`/leads/${leadId}/update`, payload);
            console.log('POST update success:', postResponse.data);
            return postResponse.data;
          } catch (postError: any) {
            console.error('POST method also failed:', postError.response?.status);
            throw error; // Throw original error
          }
        }
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      router.back();
    },
    onError: (error) => {
      console.error('Error updating lead contact:', error);
    },
  });

  const handleSelectContact = (contact: Contact) => {
    if (isEditMode) {
      // Update existing lead's contact
      updateContactMutation.mutate(contact.id);
    } else {
      // Return to the previous screen with the selected contact for new lead creation
      router.push({
        pathname: '/leads/create',
        params: {
          contact: JSON.stringify(contact)
        }
      });
    }
  };

  return (
    <>
      <Stack.Screen
        options={{
          presentation: 'modal',
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <Button
                variant="ghost"
                icon={<ArrowLeft size={24} color="#111827" />}
                onPress={() => router.back()}
              />
              <Text style={styles.headerTitle}>
                {isEditMode ? 'Change Contact' : 'Select Contact'}
              </Text>
            </View>
          ),
        }}
      />

      <View style={styles.container}>
        <ContactSelector onSelectContact={handleSelectContact} />
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
});