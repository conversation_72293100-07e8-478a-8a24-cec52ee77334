import React, { useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator } from 'react-native';
import { Stack, router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, Search, User } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { fetchUsers } from '@/lib/api';
import SearchBar from '@/components/SearchBar';
import UserAvatar from '@/components/UserAvatar';
import Button from '@/components/Button';

interface Agent {
    id: number;
    name: string;
    email: string;
    position: string;
    profile_image: string | null;
}

export default function SelectAgent() {
    const params = useLocalSearchParams();
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);

    // Get current assigned agent ID if editing
    const currentAgentId = params.currentAgentId ? parseInt(params.currentAgentId as string) : null;

    const { data, isLoading, error } = useQuery({
        queryKey: ['agents'],
        queryFn: fetchUsers,
    });

    const agents: Agent[] = data ?? [];
    
    // Filter agents based on search query
    const filteredAgents = searchQuery.length >= 2
        ? agents.filter((agent: Agent) =>
            agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            agent.email.toLowerCase().includes(searchQuery.toLowerCase())
        )
        : agents;

    const handleSelectAgent = (agent: Agent) => {
        setSelectedAgent(agent);
    };

    const handleConfirmSelection = () => {
        if (selectedAgent) {
            // Navigate back with the selected agent
            router.back();
            router.setParams({
                selectedAgent: JSON.stringify(selectedAgent)
            });
        }
    };

    const renderAgent = ({ item }: { item: Agent }) => {
        const isSelected = selectedAgent?.id === item.id;
        const isCurrent = currentAgentId === item.id;

        return (
            <TouchableOpacity
                style={[
                    styles.agentItem,
                    isSelected && styles.agentItemSelected,
                    isCurrent && styles.agentItemCurrent
                ]}
                onPress={() => handleSelectAgent(item)}
            >
                <UserAvatar
                    imageUrl={item.profile_image}
                    name={item.name}
                    size={40}
                    fontSize={16}
                />
                <View style={styles.agentInfo}>
                    <Text style={[
                        styles.agentName,
                        isSelected && styles.agentNameSelected
                    ]}>
                        {item.name}
                    </Text>
                    <Text style={styles.agentPosition}>{item.position}</Text>
                    <Text style={styles.agentEmail}>{item.email}</Text>
                    {isCurrent && (
                        <Text style={styles.currentLabel}>Currently Assigned</Text>
                    )}
                </View>
                {isSelected && (
                    <View style={styles.selectedIndicator}>
                        <View style={styles.selectedDot} />
                    </View>
                )}
            </TouchableOpacity>
        );
    };

    return (
        <>
            <Stack.Screen
                options={{
                    headerShown: true,
                    header: () => (
                        <View style={styles.header}>
                            <Button
                                variant="ghost"
                                icon={<ArrowLeft size={24} color="#111827" />}
                                onPress={() => router.back()}
                            />
                            <Text style={styles.headerTitle}>Select Agent</Text>
                            <View style={{ width: 40 }} />
                        </View>
                    ),
                }}
            />

            <View style={styles.container}>
                <View style={styles.searchContainer}>
                    <SearchBar
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholder="Search agents..."
                        icon={<Search size={20} color="#6B7280" />}
                        isLoading={isLoading}
                    />
                </View>

                {error ? (
                    <View style={styles.messageContainer}>
                        <Text style={styles.errorText}>Error loading agents</Text>
                    </View>
                ) : isLoading ? (
                    <View style={styles.messageContainer}>
                        <ActivityIndicator size="large" color="#B89C4C" />
                    </View>
                ) : filteredAgents.length === 0 ? (
                    <View style={styles.messageContainer}>
                        <Text style={styles.messageText}>
                            {searchQuery.length >= 2
                                ? "No matching agents found"
                                : "No agents available"}
                        </Text>
                    </View>
                ) : (
                    <FlatList
                        data={filteredAgents}
                        keyExtractor={(item) => item.id.toString()}
                        renderItem={renderAgent}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={styles.listContainer}
                    />
                )}

                {selectedAgent && (
                    <View style={styles.footer}>
                        <Button
                            label={`Assign to ${selectedAgent.name}`}
                            onPress={handleConfirmSelection}
                            fullWidth
                        />
                    </View>
                )}
            </View>
        </>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingTop: 60,
        paddingBottom: 16,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
    },
    headerTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#111827',
    },
    searchContainer: {
        paddingHorizontal: 16,
        paddingVertical: 16,
    },
    listContainer: {
        paddingBottom: 100,
    },
    messageContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 40,
    },
    messageText: {
        fontSize: 16,
        color: '#6B7280',
        textAlign: 'center',
    },
    errorText: {
        fontSize: 16,
        color: '#EF4444',
        textAlign: 'center',
    },
    agentItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F3F4F6',
    },
    agentItemSelected: {
        backgroundColor: '#FEF3C7',
    },
    agentItemCurrent: {
        backgroundColor: '#F0F9FF',
    },
    agentInfo: {
        flex: 1,
        marginLeft: 12,
    },
    agentName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#111827',
        marginBottom: 2,
    },
    agentNameSelected: {
        color: '#B89C4C',
    },
    agentPosition: {
        fontSize: 14,
        color: '#6B7280',
        marginBottom: 2,
    },
    agentEmail: {
        fontSize: 12,
        color: '#9CA3AF',
        marginBottom: 2,
    },
    currentLabel: {
        fontSize: 12,
        color: '#3B82F6',
        fontWeight: '500',
    },
    selectedIndicator: {
        marginLeft: 12,
    },
    selectedDot: {
        width: 12,
        height: 12,
        borderRadius: 6,
        backgroundColor: '#B89C4C',
    },
    footer: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: '#fff',
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderTopWidth: 1,
        borderTopColor: '#E5E7EB',
    },
});
