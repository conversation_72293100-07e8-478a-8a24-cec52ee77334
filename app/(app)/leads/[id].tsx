import React, { useState, useCallback, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator, Linking, Platform, TextInput } from 'react-native';
import { useLocalSearchParams, Stack, router, useFocusEffect } from 'expo-router';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ArrowLeft, Phone, Mail, MapPin, Building2, Calendar, CircleCheck, Clock, Star, User, Briefcase, Flag, Globe, Info, Hash, FileText, Edit3, Edit, Save, X, Bed, Bath, DollarSign, ListChecks } from 'lucide-react-native';
import { fetchLead, fetchLeadStatuses, api } from '@/lib/api';
import UserAvatar from '@/components/UserAvatar';
import LeadRating from '@/components/LeadRating';
import LeadStatus from '@/components/LeadStatus';


import RatingAlertModal from '@/components/RatingAlertModal';

export default function LeadDetails() {
  const params = useLocalSearchParams();
  const { id } = params;
  const leadId = typeof id === 'string' ? parseInt(id, 10) : 0;
  const queryClient = useQueryClient();

  const [showRatingModal, setShowRatingModal] = useState(false);
  const [currentDisplayStatus, setCurrentDisplayStatus] = useState<any>(null);
  const [currentDisplayRating, setCurrentDisplayRating] = useState<string>('');

  // Requirements editing state
  const [isEditingRequirements, setIsEditingRequirements] = useState(false);
  const [editedRequirements, setEditedRequirements] = useState('');

  // Contact editing state
  const [selectedContact, setSelectedContact] = useState<any>(null);
  const [selectedAgent, setSelectedAgent] = useState<any>(null);
  const [updatedRequestDetails, setUpdatedRequestDetails] = useState<any>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Handle selected contact from contact selector
  useEffect(() => {
    if (params.selectedContact) {
      try {
        const contact = JSON.parse(params.selectedContact as string);
        setSelectedContact(contact);
        setHasChanges(true);
      } catch (error) {
        console.error('Error parsing selected contact:', error);
      }
    }
  }, [params.selectedContact]);

  // Handle selected agent from agent selector
  useEffect(() => {
    if (params.selectedAgent) {
      try {
        const agent = JSON.parse(params.selectedAgent as string);
        setSelectedAgent(agent);
        setHasChanges(true);
      } catch (error) {
        console.error('Error parsing selected agent:', error);
      }
    }
  }, [params.selectedAgent]);

  // Handle updated request details
  useEffect(() => {
    if (params.updatedRequestDetails) {
      try {
        const requestDetails = JSON.parse(params.updatedRequestDetails as string);
        console.log('Received updated request details:', requestDetails);
        setUpdatedRequestDetails(requestDetails);
        setHasChanges(true);
      } catch (error) {
        console.error('Error parsing updated request details:', error);
      }
    }
  }, [params.updatedRequestDetails]);

  // Mutation for updating requirements
  const updateRequirementsMutation = useMutation({
    mutationFn: async (requirements: string) => {
      const response = await api.patch(`/leads/${leadId}`, {
        contact_id: lead?.contact_id,
        status_id: lead?.lead_status_id || 11,
        remarks: requirements,
        request: {
          propertyTypes: [lead?.filter_property_type || 1],
          operationType: lead?.filter_operation_type || 'rent'
        }
      });
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      setIsEditingRequirements(false);
    },
    onError: (error) => {
      console.error('Error updating requirements:', error);
    },
  });

  // Mutation for saving all changes (contact + other modifications)
  const saveChangesMutation = useMutation({
    mutationFn: async () => {
      const payload: any = {
        contact_id: selectedContact?.id || lead?.contact_id,
        status_id: lead?.lead_status_id || 11,
        remarks: editedRequirements || lead?.requirements || 'Updated via mobile app',
        agent_id: selectedAgent?.id || lead?.latest_assignment?.user?.id || null,
      };

      // Add request details if they were updated
      if (updatedRequestDetails) {
        // Ensure arrays are properly formatted
        const propertyTypes = Array.isArray(updatedRequestDetails.propertyTypes)
          ? updatedRequestDetails.propertyTypes.map((p: any) => parseInt(p, 10))
          : [lead?.filter_property_type || 1];

        const bedrooms = Array.isArray(updatedRequestDetails.bedrooms)
          ? updatedRequestDetails.bedrooms
          : [];

        const amenities = Array.isArray(updatedRequestDetails.selectedAmenities)
          ? updatedRequestDetails.selectedAmenities.map((a: any) => parseInt(a, 10))
          : [];

        payload.request = {
          propertyTypes,
          operationType: updatedRequestDetails.operationType || lead?.filter_operation_type || 'rent',
          amenities,
          bedrooms,
          bathrooms: updatedRequestDetails.bathrooms || '',
          maxPrice: updatedRequestDetails.maxPrice || lead?.filter_budget_max || null,
          minPrice: updatedRequestDetails.minPrice || lead?.filter_budget_min || null,
          requirements: updatedRequestDetails.requirements || editedRequirements || lead?.requirements || ''
        };
      }

      console.log('Saving changes with payload:', JSON.stringify(payload, null, 2));
      console.log('Updated request details state:', updatedRequestDetails);

      // Debug specific fields that might cause issues
      if (updatedRequestDetails) {
        console.log('propertyTypes type:', typeof updatedRequestDetails.propertyTypes, updatedRequestDetails.propertyTypes);
        console.log('bedrooms type:', typeof updatedRequestDetails.bedrooms, updatedRequestDetails.bedrooms);
        console.log('selectedAmenities type:', typeof updatedRequestDetails.selectedAmenities, updatedRequestDetails.selectedAmenities);
      }
      const response = await api.patch(`/leads/${leadId}`, payload);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['lead', leadId] });
      queryClient.invalidateQueries({ queryKey: ['leads'] });
      setSelectedContact(null);
      setSelectedAgent(null);
      setUpdatedRequestDetails(null);
      setHasChanges(false);
      setIsEditingRequirements(false);
    },
    onError: (error: any) => {
      console.error('Error saving changes:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
    },
  });

  const { data: lead, isLoading, error } = useQuery({
    queryKey: ['lead', leadId],
    queryFn: () => fetchLead(leadId),
    enabled: leadId > 0,
  });

  // Fetch lead statuses for modal
  const { data: leadStatuses = [] } = useQuery({
    queryKey: ['leadStatuses'],
    queryFn: fetchLeadStatuses,
  });

  // Set current display status when lead and leadStatuses are loaded
  React.useEffect(() => {
    if (lead && leadStatuses.length > 0) {
      const statusObj = leadStatuses.find(s => s.id.toString() === lead.lead_status_id?.toString());
      setCurrentDisplayStatus(statusObj);
    }
    if (lead) {
      setCurrentDisplayRating(lead.rating || '');
    }
  }, [lead, leadStatuses]);

  useFocusEffect(
    useCallback(() => {
      console.log('📱 Lead details screen focused, checking for data refresh...');

      const shouldRefresh = queryClient.getQueryState(['lead', leadId])?.isInvalidated;

      if (shouldRefresh) {
        console.log('🔄 Refreshing lead details due to invalidation...');
        queryClient.refetchQueries({ queryKey: ['lead', leadId] });
      }
    }, [queryClient, leadId])
  );

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#B89C4C" />
      </View>
    );
  }

  if (error || !lead) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Failed to load lead details</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => router.back()}>
          <Text style={styles.retryButtonText}>Go Back</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleCall = (phone: string, prefix: string = '') => {
    const fullNumber = `${prefix}${phone}`.replace(/\s+/g, '');
    const phoneUrl = Platform.select({
      ios: `tel:${fullNumber}`,
      android: `tel:${fullNumber}`,
      web: `tel:${fullNumber}`,
    });
    if (phoneUrl) {
      Linking.openURL(phoneUrl).catch(err => console.error('Error opening phone:', err));
    }
  };

  const handleEmail = (email: string) => {
    Linking.openURL(`mailto:${email}`).catch(err => console.error('Error opening email:', err));
  };

  let leadsRequest = null;
  try {
    if (lead.leads_request) {
      leadsRequest = JSON.parse(lead.leads_request);
    }
  } catch (e) {
    console.error('Error parsing leads_request:', e);
  }

  let parsedMetadata = null;
  try {
    if (lead.lead_metadata) {
      parsedMetadata = lead.lead_metadata;
    }
  } catch (e) {
    console.error('Error parsing lead_metadata:', e);
  }

  const getMetadataIcon = (key: string) => {
    switch (key) {
      case 'utm_source':
        return <Globe size={20} color="#4B5563" />;
      case 'utm_campaign':
        return <Flag size={20} color="#4B5563" />;
      case 'gad_source':
        return <Globe size={20} color="#4B5563" />;
      case 'network':
        return <Globe size={20} color="#4B5563" />;
      case 'fbclid':
        return <Globe size={20} color="#4B5563" />;
      case 'form_id':
        return <Info size={20} color="#4B5563" />;
      case 'form_name':
        return <Info size={20} color="#4B5563" />;
      default:
        return <Info size={20} color="#4B5563" />;
    }
  };

  const formatMetadataLabel = (key: string) => {
    return key
      .split('_')
      .map(word => {
        if (word.toLowerCase() === 'utm' || word.toLowerCase() === 'fbclid') {
          return word.toUpperCase();
        }
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join(' ');
  };

  const formatStatus = (status: string | null) => {
    if (!status) return 'New';
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Sort history in descending order by date
  const sortedHistory = [...lead.operation_history].sort((a, b) =>
    new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
  );

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          header: () => (
            <View style={styles.header}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => router.back()}
              >
                <ArrowLeft size={24} color="#111827" />
              </TouchableOpacity>
              <View style={styles.headerContent}>
                <Text style={styles.headerTitle}>Lead Details</Text>
                <View style={styles.idBadge}>
                  <Hash size={14} color="#6B7280" />
                  <Text style={styles.idText}>{lead.id}</Text>
                </View>
              </View>
            </View>
          ),
        }}
      />

      <ScrollView style={styles.container}>
        <View style={styles.hero}>
          <UserAvatar
            imageUrl={lead.contact.profile_image}
            name={lead.contact.name}
            size={80}
            fontSize={32}
          />
          <Text style={styles.heroName}>{lead.contact.name}</Text>
          <View style={styles.heroStats}>
            <TouchableOpacity onPress={() => setShowRatingModal(true)}>
              <LeadRating rating={currentDisplayRating} size="large" showLabel showEditIcon />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.statusContainer}
              onPress={() => {
                router.push({
                  pathname: '/leads/edit-status',
                  params: {
                    leadId: leadId.toString(),
                    currentStatus: currentDisplayStatus?.id?.toString() || ''
                  }
                });
              }}
            >
              <LeadStatus
                name={currentDisplayStatus?.name}
                backgroundColor={currentDisplayStatus?.background_color}
                size="large"
                showEditIcon
              />
            </TouchableOpacity>
          </View>
          {lead.inquired_ref_no && (
            <View style={styles.referenceContainer}>
              <FileText size={16} color="#6B7280" />
              <Text style={styles.referenceText}>
                Ref: {lead.inquired_ref_no}
              </Text>
            </View>
          )}
        </View>

        {(lead.latest_assignment?.user || selectedAgent) && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Assignment</Text>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => {
                  router.push({
                    pathname: '/leads/select-agent',
                    params: {
                      leadId: leadId.toString(),
                      mode: 'details',
                      currentAgentId: selectedAgent?.id || lead.latest_assignment?.user?.id || '',
                      returnTo: `/leads/${leadId}`
                    }
                  });
                }}
              >
                <Edit size={16} color="#B89C4C" />
              </TouchableOpacity>
            </View>
            <View style={styles.assignmentInfo}>
              <UserAvatar
                imageUrl={selectedAgent?.profile_image || lead.latest_assignment?.user?.profile_image}
                name={selectedAgent?.name || lead.latest_assignment?.user?.name}
                size={48}
                fontSize={18}
              />
              <View style={styles.agentInfo}>
                <Text style={styles.agentName}>
                  {selectedAgent?.name || lead.latest_assignment?.user?.name}
                  {selectedAgent && <Text style={styles.changedIndicator}> (Changed)</Text>}
                </Text>
                <Text style={styles.agentPosition}>
                  {selectedAgent?.position || lead.latest_assignment?.user?.position}
                </Text>
                <Text style={styles.assignmentDate}>
                  {selectedAgent
                    ? 'Will be assigned on save'
                    : `Assigned on ${formatDate(lead.latest_assignment?.created_at)}`
                  }
                </Text>
              </View>
            </View>
          </View>
        )}

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Contact Information</Text>
            <TouchableOpacity
              style={styles.editButton}
              onPress={() => {
                router.push({
                  pathname: '/leads/create/select-contact',
                  params: {
                    leadId: leadId.toString(),
                    mode: 'details', // Use 'details' mode to return to this screen
                    returnTo: `/leads/${leadId}`
                  }
                });
              }}
            >
              <Edit size={18} color="#6B7280" />
            </TouchableOpacity>
          </View>

          <View style={styles.contactSummary}>
            <View style={styles.infoItem}>
              <User size={20} color="#4B5563" />
              <Text style={styles.infoText}>
                {selectedContact ? selectedContact.name : lead.contact.name}
                {selectedContact && <Text style={styles.changedIndicator}> (Changed)</Text>}
              </Text>
            </View>
            {(selectedContact ? selectedContact.email_1 : lead.contact.email_1) && (
              <View style={styles.infoItem}>
                <Mail size={20} color="#4B5563" />
                <Text style={styles.infoText}>
                  {selectedContact ? selectedContact.email_1 : lead.contact.email_1}
                </Text>
              </View>
            )}
          </View>

          <View style={styles.contactInfo}>
            {lead.contact.company_name && (
              <View style={styles.infoItem}>
                <Briefcase size={20} color="#4B5563" />
                <Text style={styles.infoText}>{lead.contact.company_name}</Text>
              </View>
            )}

            {lead.contact.mobile_1 && (
              <TouchableOpacity
                style={styles.infoItem}
                onPress={() => handleCall(lead.contact.mobile_1!, lead.contact.prefix_mobile_1)}
              >
                <Phone size={20} color="#4B5563" />
                <Text style={[styles.infoText, styles.linkText]}>
                  {lead.contact.prefix_mobile_1} {lead.contact.mobile_1}
                </Text>
              </TouchableOpacity>
            )}

            {lead.contact.mobile_2 && (
              <TouchableOpacity
                style={styles.infoItem}
                onPress={() => handleCall(lead.contact.mobile_2!, lead.contact.prefix_mobile_2)}
              >
                <Phone size={20} color="#4B5563" />
                <Text style={[styles.infoText, styles.linkText]}>
                  {lead.contact.prefix_mobile_2} {lead.contact.mobile_2}
                </Text>
              </TouchableOpacity>
            )}

            {lead.contact.email_1 && (
              <TouchableOpacity
                style={styles.infoItem}
                onPress={() => handleEmail(lead.contact.email_1!)}
              >
                <Mail size={20} color="#4B5563" />
                <Text style={[styles.infoText, styles.linkText]}>{lead.contact.email_1}</Text>
              </TouchableOpacity>
            )}

            {lead.contact.email_2 && (
              <TouchableOpacity
                style={styles.infoItem}
                onPress={() => handleEmail(lead.contact.email_2!)}
              >
                <Mail size={20} color="#4B5563" />
                <Text style={[styles.infoText, styles.linkText]}>{lead.contact.email_2}</Text>
              </TouchableOpacity>
            )}

            {lead.contact.country && (
              <View style={styles.infoItem}>
                <Flag size={20} color="#4B5563" />
                <Text style={styles.infoText}>{lead.contact.country}</Text>
              </View>
            )}

            {lead.contact.address && (
              <View style={styles.infoItem}>
                <MapPin size={20} color="#4B5563" />
                <Text style={styles.infoText}>{lead.contact.address}</Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Lead Source</Text>
          <View style={styles.sourceInfo}>
            <View style={styles.infoItem}>
              <Globe size={20} color="#4B5563" />
              <Text style={styles.infoText}>Platform: {lead.resolved_platform}</Text>
            </View>
          </View>
        </View>

        {parsedMetadata && Object.keys(parsedMetadata).some(key => parsedMetadata[key]) && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Lead Metadata</Text>
            <View style={styles.metadataContainer}>
              {Object.entries(parsedMetadata).map(([key, value]) => {
                if (value) {
                  return (
                    <View key={key} style={styles.metadataItem}>
                      <View style={styles.metadataHeader}>
                        {getMetadataIcon(key)}
                        <Text style={styles.metadataLabel}>
                          {formatMetadataLabel(key)}
                        </Text>
                      </View>
                      <Text style={styles.metadataValue}>{value}</Text>
                    </View>
                  );
                }
                return null;
              })}
            </View>
          </View>
        )}

        {leadsRequest && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>
                Request Details
                {updatedRequestDetails && <Text style={styles.changedIndicator}> (Changed)</Text>}
              </Text>
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => {
                  router.push({
                    pathname: '/leads/create/details',
                    params: {
                      leadId: leadId.toString(),
                      mode: 'details',
                      adType: leadsRequest.ot || 'rent',
                      returnTo: `/leads/${leadId}`
                    }
                  });
                }}
              >
                <Edit size={18} color="#6B7280" />
              </TouchableOpacity>
            </View>
            <View style={[styles.requestInfo, updatedRequestDetails && styles.previewContainer]}>
              {updatedRequestDetails ? (
                // Show updated request details preview
                <>
                  {updatedRequestDetails.operationType && (
                    <View style={styles.requestItem}>
                      <Building2 size={20} color="#B89C4C" />
                      <Text style={[styles.requestText, styles.previewText]}>
                        Operation Type: {updatedRequestDetails.operationType.toUpperCase()}
                      </Text>
                    </View>
                  )}
                  {updatedRequestDetails.propertyTypes && updatedRequestDetails.propertyTypes.length > 0 && (
                    <View style={styles.requestItem}>
                      <Building2 size={20} color="#B89C4C" />
                      <Text style={[styles.requestText, styles.previewText]}>
                        Property Types: {updatedRequestDetails.propertyTypes.length} selected
                      </Text>
                    </View>
                  )}
                  {updatedRequestDetails.bedrooms && updatedRequestDetails.bedrooms.length > 0 && (
                    <View style={styles.requestItem}>
                      <Bed size={20} color="#B89C4C" />
                      <Text style={[styles.requestText, styles.previewText]}>
                        Bedrooms: {updatedRequestDetails.bedrooms.join(', ')}
                      </Text>
                    </View>
                  )}
                  {updatedRequestDetails.bathrooms && (
                    <View style={styles.requestItem}>
                      <Bath size={20} color="#B89C4C" />
                      <Text style={[styles.requestText, styles.previewText]}>
                        Bathrooms: {updatedRequestDetails.bathrooms}
                      </Text>
                    </View>
                  )}
                  {(updatedRequestDetails.minPrice || updatedRequestDetails.maxPrice) && (
                    <View style={styles.requestItem}>
                      <DollarSign size={20} color="#B89C4C" />
                      <Text style={[styles.requestText, styles.previewText]}>
                        Budget: {updatedRequestDetails.minPrice || 'Any'} - {updatedRequestDetails.maxPrice || 'Any'}
                      </Text>
                    </View>
                  )}
                  {updatedRequestDetails.selectedAmenities && updatedRequestDetails.selectedAmenities.length > 0 && (
                    <View style={styles.requestItem}>
                      <ListChecks size={20} color="#B89C4C" />
                      <Text style={[styles.requestText, styles.previewText]}>
                        Amenities: {updatedRequestDetails.selectedAmenities.length} selected
                      </Text>
                    </View>
                  )}
                </>
              ) : (
                // Show original request details
                <>
                  {leadsRequest.ot && (
                    <View style={styles.requestItem}>
                      <Building2 size={20} color="#4B5563" />
                      <Text style={styles.requestText}>
                        Operation Type: {leadsRequest.ot.toUpperCase()}
                      </Text>
                    </View>
                  )}
                  {lead.filter_property_type && (
                    <View style={styles.requestItem}>
                      <Building2 size={20} color="#4B5563" />
                      <Text style={styles.requestText}>
                        Property Type: {lead.filter_property_type}
                      </Text>
                    </View>
                  )}
                  {leadsRequest.be && leadsRequest.be.length > 0 && (
                    <View style={styles.requestItem}>
                      <Bed size={20} color="#4B5563" />
                      <Text style={styles.requestText}>
                        Bedrooms: {(Array.isArray(leadsRequest.be) ? leadsRequest.be : []).join(', ')}
                      </Text>
                    </View>
                  )}
                  {leadsRequest.ba && (
                    <View style={styles.requestItem}>
                      <Bath size={20} color="#4B5563" />
                      <Text style={styles.requestText}>
                        Bathrooms: {leadsRequest.ba}
                      </Text>
                    </View>
                  )}
                  {lead.filter_budget_min || lead.filter_budget_max ? (
                    <View style={styles.requestItem}>
                      <DollarSign size={20} color="#4B5563" />
                      <Text style={styles.requestText}>
                        Budget: {lead.filter_budget_min || 'Any'} - {lead.filter_budget_max || 'Any'}
                      </Text>
                    </View>
                  ) : null}
                  {leadsRequest.a && leadsRequest.a.length > 0 && (
                    <View style={styles.requestItem}>
                      <ListChecks size={20} color="#4B5563" />
                      <Text style={styles.requestText}>
                        Amenities: {leadsRequest.a.length} selected
                      </Text>
                    </View>
                  )}
                </>
              )}
            </View>
          </View>
        )}



        {lead.requirements && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Requirements</Text>
              <View style={styles.requirementsActions}>
                {isEditingRequirements && (
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => {
                      setIsEditingRequirements(false);
                      setEditedRequirements('');
                    }}
                  >
                    <X size={18} color="#EF4444" />
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => {
                    if (isEditingRequirements) {
                      // Save requirements
                      updateRequirementsMutation.mutate(editedRequirements);
                    } else {
                      // Start editing
                      setEditedRequirements(lead.requirements || '');
                      setIsEditingRequirements(true);
                    }
                  }}
                >
                  {isEditingRequirements ? (
                    <Save size={18} color="#22C55E" />
                  ) : (
                    <Edit size={18} color="#6B7280" />
                  )}
                </TouchableOpacity>
              </View>
            </View>

            {isEditingRequirements ? (
              <TextInput
                style={[styles.requirements, styles.requirementsInput]}
                value={editedRequirements}
                onChangeText={(text) => {
                  setEditedRequirements(text);
                  setHasChanges(true);
                }}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            ) : (
              <Text style={styles.requirements}>{lead.requirements}</Text>
            )}
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>History</Text>
          {sortedHistory.map((history) => (
            <View key={history.id} style={styles.historyItem}>
              <View style={styles.historyHeader}>
                {history.content.includes('status updated') ? (
                  <CircleCheck size={20} color="#10B981" />
                ) : (
                  <Clock size={20} color="#6B7280" />
                )}
                <Text style={styles.historyContent}>{history.content}</Text>
              </View>
              <Text style={styles.historyDate}>
                {formatDate(history.created_at)}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Save button - appears when there are changes */}
      {hasChanges && (
        <TouchableOpacity
          style={styles.saveButton}
          onPress={() => saveChangesMutation.mutate()}
          disabled={saveChangesMutation.isPending}
        >
          <Text style={styles.saveButtonText}>
            {saveChangesMutation.isPending ? 'Saving...' : 'Save Changes'}
          </Text>
        </TouchableOpacity>
      )}

      {/* Status editing is now handled by navigation to edit-status screen */}

      {/* Rating Alert Modal */}
      <RatingAlertModal
        visible={showRatingModal}
        onClose={() => setShowRatingModal(false)}
        selectedRating={currentDisplayRating}
        onSelectRating={(ratingId: string) => {
          setCurrentDisplayRating(ratingId);
          setShowRatingModal(false);
        }}
      />
    </>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    marginRight: 16,
  },
  editButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F9F9F9',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  headerContent: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
  },
  idBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  idText: {
    fontSize: 13,
    color: '#4B5563',
    fontWeight: '500',
  },
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  hero: {
    backgroundColor: '#fff',
    padding: 24,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  heroName: {
    fontSize: 24,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 12,
  },
  heroStats: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  statusEditButton: {
    padding: 4,
    borderRadius: 4,
    backgroundColor: '#F3F4F6',
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 6,
  },
  badgeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  referenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    gap: 8,
  },
  referenceText: {
    fontSize: 14,
    color: '#4B5563',
    fontWeight: '500',
  },
  section: {
    padding: 20,
    backgroundColor: '#fff',
    marginTop: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  assignmentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  agentInfo: {
    flex: 1,
  },
  agentName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  agentPosition: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  assignmentDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  contactSummary: {
    gap: 12,
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  contactInfo: {
    gap: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  infoText: {
    fontSize: 16,
    color: '#4B5563',
    flex: 1,
  },
  linkText: {
    color: '#2563EB',
    textDecorationLine: 'underline',
  },
  sourceInfo: {
    gap: 16,
  },
  metadataContainer: {
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    overflow: 'hidden',
  },
  metadataItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  metadataHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  metadataLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  metadataValue: {
    fontSize: 16,
    color: '#111827',
    marginLeft: 28,
  },
  requestInfo: {
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
    gap: 12,
  },
  requestItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  requestText: {
    fontSize: 14,
    color: '#4B5563',
  },
  requirements: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
    backgroundColor: '#F3F4F6',
    padding: 16,
    borderRadius: 8,
  },
  requirementsInput: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    minHeight: 100,
  },
  requirementsActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  cancelButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: '#FEF2F2',
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  historyItem: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
  },
  historyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  historyContent: {
    flex: 1,
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  historyDate: {
    fontSize: 12,
    color: '#6B7280',
  },
  changedIndicator: {
    color: '#B89C4C',
    fontWeight: '600',
    fontSize: 12,
  },
  saveButton: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: '#B89C4C',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  previewContainer: {
    backgroundColor: '#FEF3C7',
    borderWidth: 1,
    borderColor: '#F59E0B',
    borderRadius: 8,
  },
  previewText: {
    color: '#92400E',
    fontWeight: '500',
  },
});