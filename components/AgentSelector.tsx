import React, { useState } from 'react';
import { View, Text, StyleSheet, Modal, TouchableOpacity, FlatList, ActivityIndicator } from 'react-native';
import { X, Search, User } from 'lucide-react-native';
import { useQuery } from '@tanstack/react-query';
import { api, fetchUsers } from '@/lib/api';
import SearchBar from '@/components/SearchBar';
import UserAvatar from '@/components/UserAvatar';

interface Agent {
    id: number;
    name: string;
    email: string;
    position: string;
    profile_image: string | null;
}

interface AgentSelectorProps {
    visible: boolean;
    onClose: () => void;
    onSelect: (agent: Agent) => void;
    selectedAgentId?: number;
}

export default function AgentSelector({
    visible,
    onClose,
    onSelect,
    selectedAgentId,
}: AgentSelectorProps) {
    const [searchQuery, setSearchQuery] = useState('');

    const { data, isLoading, error } = useQuery({
        queryKey: ['agents'],
        queryFn: fetchUsers,
    });

    const agents: Agent[] = data ?? [];
    
    // Filter agents based on search query
    const filteredAgents = searchQuery.length >= 2
        ? agents.filter((agent: Agent) =>
            agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            agent.email.toLowerCase().includes(searchQuery.toLowerCase())
        )
        : agents;

    const handleSelect = (agent: Agent) => {
        onSelect(agent);
        onClose();
    };

    const handleModalClose = () => {
        onClose();
        setSearchQuery('');
    };

    return (
        <Modal
            visible={visible}
            transparent
            animationType="slide"
            onRequestClose={handleModalClose}
        >
            <View style={styles.overlay}>
                <View style={styles.modal}>
                    <View style={styles.header}>
                        <Text style={styles.title}>Select Agent</Text>
                        <TouchableOpacity
                            onPress={handleModalClose}
                            hitSlop={{ top: 10, right: 10, bottom: 10, left: 10 }}
                        >
                            <X size={24} color="#6B7280" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.searchContainer}>
                        <SearchBar
                            value={searchQuery}
                            onChangeText={setSearchQuery}
                            placeholder="Search agents..."
                            icon={<Search size={20} color="#6B7280" />}
                            isLoading={isLoading}
                        />
                    </View>

                    {error ? (
                        <View style={styles.messageContainer}>
                            <Text style={styles.errorText}>Error loading agents</Text>
                        </View>
                    ) : isLoading ? (
                        <View style={styles.messageContainer}>
                            <ActivityIndicator size="large" color="#B89C4C" />
                        </View>
                    ) : filteredAgents.length === 0 ? (
                        <View style={styles.messageContainer}>
                            <Text style={styles.messageText}>
                                {searchQuery.length >= 2
                                    ? "No matching agents found"
                                    : "No agents available"}
                            </Text>
                        </View>
                    ) : (
                        <FlatList
                            data={filteredAgents}
                            keyExtractor={(item) => item.id.toString()}
                            renderItem={({ item }) => (
                                <TouchableOpacity
                                    style={[
                                        styles.agentItem,
                                        selectedAgentId === item.id && styles.agentItemSelected
                                    ]}
                                    onPress={() => handleSelect(item)}
                                >
                                    <UserAvatar
                                        imageUrl={item.profile_image}
                                        name={item.name}
                                        size={40}
                                        fontSize={16}
                                    />
                                    <View style={styles.agentInfo}>
                                        <Text style={[
                                            styles.agentName,
                                            selectedAgentId === item.id && styles.agentNameSelected
                                        ]}>
                                            {item.name}
                                        </Text>
                                        <Text style={styles.agentPosition}>{item.position}</Text>
                                        <Text style={styles.agentEmail}>{item.email}</Text>
                                    </View>
                                    {selectedAgentId === item.id && (
                                        <View style={styles.selectedIndicator}>
                                            <View style={styles.selectedDot} />
                                        </View>
                                    )}
                                </TouchableOpacity>
                            )}
                            showsVerticalScrollIndicator={false}
                        />
                    )}
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
    },
    modal: {
        backgroundColor: '#fff',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        maxHeight: '70%',
        paddingTop: 20,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingBottom: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
    },
    title: {
        fontSize: 18,
        fontWeight: '600',
        color: '#111827',
    },
    searchContainer: {
        paddingHorizontal: 20,
        paddingVertical: 15,
    },
    messageContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 40,
    },
    messageText: {
        fontSize: 16,
        color: '#6B7280',
        textAlign: 'center',
    },
    errorText: {
        fontSize: 16,
        color: '#EF4444',
        textAlign: 'center',
    },
    agentItem: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#F3F4F6',
    },
    agentItemSelected: {
        backgroundColor: '#FEF3C7',
    },
    agentInfo: {
        flex: 1,
        marginLeft: 12,
    },
    agentName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#111827',
        marginBottom: 2,
    },
    agentNameSelected: {
        color: '#B89C4C',
    },
    agentPosition: {
        fontSize: 14,
        color: '#6B7280',
        marginBottom: 2,
    },
    agentEmail: {
        fontSize: 12,
        color: '#9CA3AF',
    },
    selectedIndicator: {
        marginLeft: 12,
    },
    selectedDot: {
        width: 12,
        height: 12,
        borderRadius: 6,
        backgroundColor: '#B89C4C',
    },
});
